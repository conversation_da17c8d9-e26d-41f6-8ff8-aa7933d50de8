<div class="steps-section">
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-1.png') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Social Media</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-2.png') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Influencer Tasks</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-4-new.svg') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Campaign details</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-3-new.svg') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Marketplace</div>
        <div class="steps-position"></div>
    </div>
    <div class="steps-point">
        <div class="steps-icon">
            <img src="{{ asset('/assets/front-end/images/icons/mp-step-icon-5.png') }}" class="step-icon" alt="">
            <img src="{{ asset('/assets/front-end/images/icons/mp-checkmark-circle.svg') }}" class="step-icon-check" alt="">
        </div>
        <div class="steps-cont"></div>
        <div class="steps-which">Payment details</div>
        <div class="steps-position"></div>
    </div>
</div>

{{-- MP-Steps --}}
<input type="hidden" name="inputName" id="soc-type" value="" placeholder="">
<div class="this-steps">
    {{-- Step one --}}
    <div class="page_tab new_steps">
        <div class="mobile-step-detail">
            <div class="steps-cont">
                {{-- STEP 01 --}}
            </div>
            <div class="steps-which">
                Social Media
            </div>
        </div>
        <span id="error-select-social-media"></span>
        <span id="error-post-type"></span>
        <div class="social_media d-flex align-items-center justify-content-between flex-wrap">
            <div class="social_media_radio instagram" style="pointer-events: auto; filter: blur(0);">
                <span class="media_platform">
                    <input type="radio"
                        name="mp_socialmedia"
                        data-social-media="instagram"
                        data-parsley-errors-container="#error-select-social-media"
                        data-parsley-error-message="Please select social media."
                        required
                        value="instagram">
                    <label>
                        <img
                            src="{{ asset('/assets/front-end/images/icons/new_social_media_instagram.png') }}"
                            class="mp-socialmedia-icon"
                            alt=""> Instagram
                    </label>
                </span>
                <span class="media_platform_post_type">
                    <div class="get_type content">
                        <div class="form-group">
                            <input type="radio" name="mp_socialmedia_type2"
                                data-parsley-errors-container="#error-post-type"
                                data-parsley-error-message="Please select post type." required value="Story">
                            <label>Story</label>
                        </div>
                    </div>
                    <div class="get_type photo">
                        <div class="form-group">
                            <input type="radio" name="mp_socialmedia_type2" value="Story - Picture">
                            <label>Story - Picture</label>
                        </div>
                    </div>
                    <div class="get_type video">
                        <div class="form-group">
                            <input type="radio" name="mp_socialmedia_type2" value="Story - Video">
                            <label>Story - Video</label>
                        </div>
                    </div>
                    <div class="get_type reaction-video">
                        <div class="form-group">
                            <input type="radio" name="mp_socialmedia_type2" value="Reel">
                            <label>Reel</label>
                        </div>
                    </div>
                    <div class="get_type survey">
                        <div class="form-group">
                            <input type="radio" name="mp_socialmedia_type2" value="Story - Picture">
                            <label>Story - Picture</label>
                        </div>
                        <div class="form-group">
                            <input type="radio" name="mp_socialmedia_type2" value="Story - Video">
                            <label>Story - Video</label>
                        </div>
                    </div>

                </span>
            </div>
            <div class="social_media_radio facebook">
                <span class="media_platform">
                    <input type="radio" name="mp_socialmedia" data-social-media="facebook" value="facebook">
                    <label><img src="{{ asset('/assets/front-end/images/icons/new_social_media_facebook.png') }}" class="mp-socialmedia-icon" alt=""> Facebook</label>
                </span>
            </div>
            <div class="social_media_radio youtube">
                <span class="media_platform">
                    <input type="radio" name="mp_socialmedia" data-social-media="youtube" value="youtube">
                    <label><img src="{{ asset('/assets/front-end/images/icons/new_social_media_youtube.png') }}"
                            class="mp-socialmedia-icon" alt=""> YouTube</label>
                </span>
            </div>
            <div class="social_media_radio tiktok">
                <span class="media_platform">
                    <input type="radio" name="mp_socialmedia" data-social-media="tiktok">
                    <label><img src="{{ asset('/assets/front-end/images/icons/new_social_media_tiktok.png') }}"
                            class="mp-socialmedia-icon" alt=""> TikTok</label>
                </span>
            </div>
        </div>
        <div class="step-nevigationbutton">
            <div class="nav-left" data-from-step="">
                <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" class="" alt="">
            </div>
            <div class="nav-right">
                <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" class="" alt="">
            </div>
        </div>
    </div>
    {{-- Step two --}}
    <div class="page_tab new_steps">
        <div class="mobile-step-detail">
            <div class="steps-cont">
                {{-- STEP 01 --}}
            </div>
            <div class="steps-which">
                Task
            </div>
        </div>
        <div class="task-options">
            <div class="">
                <div class="outer-task" id="latest-task">
                </div>
            </div>
        </div>
        <div class="step-nevigationbutton">
            <div class="nav-left" data-from-step="">
                <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" class="" alt="">
            </div>
            <div class="nav-right">
                <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" class="" alt="">
            </div>
        </div>
    </div>
    {{-- Step three --}}
    <div class="page_tab new_steps">
        <div class="mobile-step-detail">
            <div class="steps-cont">
                {{-- STEP 01 --}}
            </div>
            <div class="steps-which">
                Campaign details
            </div>
        </div>
        <div class="task-options new" id="latest-task-inputs">


        </div>
        <div class="step-nevigationbutton">
            <div class="nav-left" data-from-step="">
                <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" class="" alt="">
            </div>
            <div class="nav-right">
                <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" class="" alt="">
            </div>
        </div>
    </div>
    {{-- Step four --}}
    <div class="page_tab new_steps">
        <div class="mobile-step-detail">
            <div class="steps-cont">
                {{-- STEP 01 --}}
            </div>
            <div class="steps-which">
                Marketplace
            </div>
        </div>
        <div id="latest-influencer-lists">

        </div>
        <input type="hidden" id="delected-influncer" value=""
            data-parsley-errors-container="#error-select-influncer" data-parsley-error-message=" " required>
        <div class="step-nevigationbutton">
            <div class="nav-left" data-from-step="">
                <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" class="" alt="">
            </div>
            <div class="backtop" id="backtop">
                <img src="{{ asset('/assets/front-end/images/icons/warenkorb_button.png') }}" class="" alt="">
            </div>
            <div class="influncer-cart">
                <span class="influncer-cart-count">0</span>
                <img src="{{ asset('/assets/front-end/images/icons/influncer-cart.svg') }}" class="" alt="">
            </div>

            <div class="selected-influncer-box">
                <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" class="close_inf_box" alt="">
                <div class="selected-influncer-contr"></div>
                <div class="selected-influncer-data">
                    <div class="data-detail">
                        <div class="uj">
                            <img src="{{ asset('/assets/front-end/images/icons/req-money.svg') }}" class="" alt="">
                        </div>
                        Subtotal:<span id="subtotal" data-subtotal="0">0.00</span> €
                    </div>
                    <div class="data-detail">
                        <div class="uj">
                            <img src="{{ asset('/assets/front-end/images/icons/icon-eye.svg') }}" class="" alt="">
                        </div>
                        <span id="follower" data-followers="0">0</span> follower
                    </div>
                </div>
            </div>
            <span id="error-select-influncer d-none"></span>
            <div class="nav-right">
                <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" class="" alt="">
            </div>
        </div>
        <div class="filter-overlay"></div>
    </div>
    {{-- Step five --}}
    <div class="page_tab new_steps">
        <div class="mobile-step-detail">
            <div class="steps-cont">
                {{-- STEP 01 --}}
            </div>
            <div class="steps-which">
                Payment details
            </div>
        </div>
        <div class="marketplace-finish">
            <style>
                @media screen and (max-width: 768px) {
                    .campaign-list {
                        font-size: 7px;
                    }
                }
            </style>
            <div id="influencers-row">
            </div>
            <div class="order-summary">
                <div class="summary-column" style="width:auto;">
                    <div class="summary-item">
                        <span class="label">Subtotal:</span>&nbsp;
                        <span class="value"><span id="follower_final_new" data-followers="0">0</span> Followers</span>
                    </div>
                </div>
                <div class="summary-column" style="display: flex; flex-direction:column; width:auto;">
                    <div class="summary-item">
                        <span class="label">Price:</span>&nbsp;
                        <span class="value">€ <span id="subtotal_final_new" data-subtotal="0">0</span> (VAT excluded)</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Platform Fee (5%): &nbsp;</span>
                        <span class="value">€ <span id="fee_final_new" data-fee="0">0</span> (VAT excluded)</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">VAT:</span>
                        <span class="value">€ <span id="vat_final_new" data-vat="0">0</span></span>
                    </div>
                </div>
            </div>
            <div class="total-container">
                <div class="total-item">
                    <span class="total-label">Order Total:</span>
                    <span class="total-value" style="text-align: end;">
                        € <span id="total_final_new" data-total="0">0</span> (VAT included)
                    </span>
                </div>
            </div>
            <div class="info-text">
                <p>** You are starting the request phase. Influencer can accept or reject your request. Depending on
                    this, your order total will change. After the request phase you will be able to pay and start the
                    campaign. **</p>
            </div>
            <div class="form-shadow-box middle-box" style="display: none;">
                <span id="ifdonthave" style="display: none;"> </span>
                <table border="0" cellpadding="0" cellspacing="0">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Influencer Name</th>
                            <th>Follower</th>
                            <th colspan="2">Price</th>
                        </tr>
                    </thead>
                    <tbody>

                    </tbody>

                    <tfoot>
                        <tr>
                            <td></td>
                            <td></td>
                            <td class="dark">Order Total:</td>
                            <td class="dark" colspan="2">
                                <div class="lim-width">
                                    <span id="total_final" data-total="0">0</span> € (VAT included)
                                    <input type="hidden" name="total_amount" id="total_input"></div>
                            </td>
                        </tr>
                    </tfoot>
                    <tfoot>
                        <tr>
                            <td></td>
                            <td></td>
                            <td><span id="follower_final" data-followers="0">0</span> Follower</td>
                            <td colspan="2">
                                <div class="lim-width">
                                    <span id="subtotal_final" data-subtotal="0">0</span> € (VAT included)
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td></td>
                            <td></td>
                            <td>Platform Fee:</td>
                            <td colspan="2">
                                <div class="lim-width"><span id="fee_final" data-fee="0">0</span> € <input type="hidden"
                                        name="fee_input" id="fee_input">(VAT included)</div>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <div class="terms-div middle-box text-center" style="display: none;">
                ** You are starting the request phase. Influencer can accept or reject your request. Depending on this,
                your order total will change. After the request phase you will be able to pay and start the campaign. **
            </div>
            <div class="step-nevigationbutton" style="position: relative; bottom:0;">
                <div class="nav-left" data-from-step="">
                    <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" class="" alt="">
                </div>
                <div class="submit-button-infl">
                    <button type="submit" class="button-Request">Request</button>
                </div>
                <div class="nav-right opacity-0 pe-none">
                    <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" class="" alt="">
                </div>
            </div>
        </div>
    </div>
</div>
<!--social connect error popup Start end-->
<div class="loaderss" id="pageLoader">
    <img src="{{ asset('/assets/front-end/images/loading-loading-forever.gif') }}" alt="">
</div>


<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>


<script>
    // Constants for calculations
    const VAT_RATE = 0.19;
    const ADMIN_COMMISSION_RATE = 5;
    const MIN_PLATFORM_FEE = 2;

    // Helper functions for calculations
    function calculatePlatformFee(subtotal) {
        let fee = (subtotal * ADMIN_COMMISSION_RATE) / 100;
        return fee < MIN_PLATFORM_FEE ? MIN_PLATFORM_FEE : fee;
    }

    function parseFloatSafe(value) {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
    }

    function parseIntSafe(value) {
        const parsed = parseInt(value);
        return isNaN(parsed) ? 0 : parsed;
    }

    function formatNumberWithThousandSeparator(value) {
        return parseFloatSafe(value).toLocaleString('de-DE', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    function updateTotals() {
        // Get values from data attributes instead of text
        const subtotal = parseFloatSafe($('#subtotal').data('subtotal'));
        const follower = parseIntSafe($('#follower').data('followers'));
        
        // Update all subtotal and follower displays with formatted text and data attributes
        $('#subtotal_final').text(formatNumberWithThousandSeparator(subtotal)).data('subtotal', subtotal);
        $('#subtotal_final_new').text(formatNumberWithThousandSeparator(subtotal)).data('subtotal', subtotal);
        $('#follower_final').text(follower).data('followers', follower);
        $('#follower_final_new').text(follower).data('followers', follower);
        
        // Calculate platform fee
        const platformFee = calculatePlatformFee(subtotal);
        const platformFeeVAT = platformFee * VAT_RATE;
        
        // Calculate total VAT from selected influencers
        let influencerVAT = 0;
        $(".selected-influncer-contr .influncer-detail").each(function() {
            const price = parseFloatSafe($(this).find(".user_price-val").data("price"));
            const isSmallBusiness = Boolean(parseInt($(this).find("#is_small_business_owner").text() || "0"));
            if (!isSmallBusiness) {
                influencerVAT += price * VAT_RATE;
            }
        });
        
        const totalVAT = influencerVAT + platformFeeVAT;
        const totalPrice = subtotal + platformFee + totalVAT;
        
        // Update all price displays with formatted text and data attributes
        $('#vat_final_new').text(formatNumberWithThousandSeparator(totalVAT)).data('vat', totalVAT);
        $('#total_final').text(formatNumberWithThousandSeparator(totalPrice)).data('total', totalPrice);
        $('#total_final_new').text(formatNumberWithThousandSeparator(totalPrice)).data('total', totalPrice);
        $('#fee_final').text(formatNumberWithThousandSeparator(platformFee)).data('fee', platformFee);
        $('#fee_final_new').text(formatNumberWithThousandSeparator(platformFee)).data('fee', platformFee);
        $('#total_input').val(totalPrice.toFixed(2));
        $('#fee_input').val(platformFee.toFixed(2));
    }

    // Initialize step indicators (only if not already initialized)
    var mp_step = $('.steps-section .steps-point');

    var iEra, thumbLi = $('.steps-section .steps-point');
    var iEra_in, thumbLi_in = $('.steps-section .steps-point .steps-cont');

    // Only initialize if IDs are not already set
    if (!thumbLi.first().attr("id")) {
        for (iEra = 0; iEra < mp_step.length; iEra++) {
            thumbLi.eq(iEra).attr("id", 'steps-point' + iEra);
        }
        for (iEra_in = 0; iEra_in < mp_step.length; iEra_in++) {
            thumbLi_in.eq(iEra_in).html('STEP 0' + (iEra_in + 1));
        }
    }

    $('input[name="ptc"]').click(function() {
        var select_val = $('input[name="ptc"]:checked').val();
        $(".media_platform_post_type").find(".get_type").hide();
        $(".media_platform_post_type").find(".get_type." + select_val).show();
        $("#steps-point0").addClass("inprogress");
        $("#new_steps0").show();
    });

    $('input[name="mp_socialmedia"]').click(function() {
        if ($(this).is(':checked')) {
            $(".social_media_radio").removeClass("active")
            $(this).closest(".social_media_radio").addClass("active")
            $(".media_platform_post_type").hide()
            $(this).closest(".media_platform").next(".media_platform_post_type").css("display", "flex");
        }
    });

    $(document).on("click", ".button-open-filter", function() {
        $(".filter-button-outer").hide()
        $(".filter-box-outer").show()
        $("select.filter-by").select2();
        $("select").select2();
    });

    $(document).on("click", ".button-close-filter", function() {
        $(".filter-button-outer").show()
        $(".filter-box-outer").hide()
    });

    $(document).on("click", ".step-nevigationbutton > div.influncer-cart", function() {
        $(".selected-influncer-box").toggleClass("open")
        $(".filter-overlay").toggleClass("open")
    });

    $(document).on("click", ".step-nevigationbutton > div > img.close_inf_box", function() {
        $(".selected-influncer-box").toggleClass("open")
        $(".filter-overlay").toggleClass("open")
    });

    let max_influcencer_counter = 0;

    $(document).on("click", ".influncer-detail button.select-button", function(event) {
        if (max_influcencer_counter >= 50) {
            toastr.info("Maximum number of influencer limit reached. ");
            return;
        }
        
        const $influencerDetail = $(this).closest(".influncer-detail");
        const get_influncer_id = $influencerDetail.attr("data-id");
        const get_influncer_image = $influencerDetail.find(".profile-pic").html();
        const get_influncer_image_new = $influencerDetail.find(".profile-pic img").attr("src");
        const get_influncer_username = $influencerDetail.find(".user_name").text();
        const is_small_business_owner = Boolean(parseInt($influencerDetail.find("#is_small_business_owner").text() || "0"));
        const get_influncer_follower = parseIntSafe($influencerDetail.find(".follower-count-val").data("followers")) || 0;
        const get_influncer_price = parseFloatSafe($influencerDetail.find(".user_price-val").data("price")) || 0;

        // Update subtotal and follower count
        const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
        const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
        const newSubtotal = currentSubtotal + get_influncer_price;
        const newFollower = currentFollower + get_influncer_follower;

        // Update with formatted text and data attributes
        $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#follower').text(newFollower).data('followers', newFollower);

        console.log('subtotal: ' + newSubtotal);
        console.log('follower: ' + newFollower);
        console.log('get_influencer_price: ' + get_influncer_price);
        console.log('get_influencer_follower: ' + get_influncer_follower);

        // Add to selected influencers
        var influncer_div = "<div class='influncer-detail card-container text-center mx-auto mt-4' data-id='" +
            get_influncer_id + "'>" + $influencerDetail.html() + "</div>";
        $(".selected-influncer-contr").append(influncer_div);
        $("span.influncer-cart-count").text($(".selected-influncer-contr .influncer-detail").length);
        $(".selected-influncer-contr .influncer-detail button").text("Remove").removeClass("select-button").addClass("remove-button");
        $influencerDetail.addClass("selected");

        $(this).text("UNSELECT").removeClass("select-button").addClass("unselect-button");

        $("#delected-influncer").val($(".selected-influncer-contr .influncer-detail").length);

        const toggleImage = document.getElementById(`card_clickitfame_logo_${get_influncer_id}`);
        if (toggleImage) {
            toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_white.svg') }}";
        }

        // Add to influencer list
        const influncer_id = get_influncer_id.replace(/inflinser_id/, '');
        const VAT_value = get_influncer_price * VAT_RATE;

        let small_business_owner_pricing =
            '<span id="vat_value" data-vat="0">0</span> € <span style="font-size: 8px; font-weight:100;">(Small business owner according to § 19 UStG)</span>';
        let non_small_business_owner_pricing =
            `<span id="vat_value" data-vat="${VAT_value.toFixed(2)}">${formatNumberWithThousandSeparator(VAT_value)}</span> € <span style="font-size: 8px; font-weight:100;">(VAT 19%)</span>`;

        let VAT_text = is_small_business_owner ? small_business_owner_pricing : non_small_business_owner_pricing;

        // Create influencer list row with data attributes
        var influncer_list_row_new = '<div class="campaign-list ' + get_influncer_id + '">\
                <div class="campaign-item row">\
                    <div class="campaign-influencer col-5" style="margin: 0; display:flex; align-items: center;">\
                        <img src="' + get_influncer_image_new + '" alt="Influencer" class="influencer-pic">\
                        <div class="influencer-details">\
                            <span class="influencer-name" target="_blank" style="color: #212529; ">' +
            get_influncer_username + '</span>\
                            <span class="follower-count-val" data-followers="' + get_influncer_follower + '"><span id="follower-count-val-' + get_influncer_id + '">' + get_influncer_follower + '</span> Followers</span>\
                        </div>\
                    </div>\
                    <div class="campaign-pricing col-6" style="margin: 0; padding:0; display:flex;">\
                        <div class="row w-100">\
                            <span class="user_price-val col-4" data-price="' + get_influncer_price + '">\
                                € <span id="user_price-val-' + get_influncer_id + '">' + formatNumberWithThousandSeparator(get_influncer_price) + '</span></span>\
                            <span class="campaign_vat_price col-8" style="margin: 0; padding:0;">' + VAT_text + '</span>\
                        </div>\
                     </div>\
                   <div class="remove-icon col-1" style="margin: 0; padding:0; text-align: center;">\
                          <img src="{{ asset("/assets/front-end/images/new/delete.svg") }}" data-delete="' +
            get_influncer_id + '">\
                   </div>\
                </div>\
            </div>';
        var influncer_list_row = '<tr class="influncer-detail-final ' + get_influncer_id +
            '"><input type="hidden" name="influncer_selected_id[]" value="' + influncer_id + '">\
                            <td>\
                                <div class="finish-image"> ' + get_influncer_image + ' </div>\
                            </td>\
                            <td class="influncer-name">\
                                ' + get_influncer_username + '\
                            </td>\
                            <td class="total-follower">\
                                <span class="follower-count-val" data-followers="' + get_influncer_follower + '">' + get_influncer_follower + '</span> Follower\
                            </td>\
                            <td class="pricing">\
                                <span class="user_price-val" data-price="' + get_influncer_price + '">' + formatNumberWithThousandSeparator(get_influncer_price) + '</span> €\
                            </td>\
                            <td class="remove-list">\
                                <img src="{{ asset("assets/front-end/images/icons/icon-delete.svg") }}" data-delete="' + get_influncer_id + '" alt="">\
                            </td>\
                        </tr>';

        $(".form-shadow-box.middle-box table tbody").append(influncer_list_row);
        $('#influencers-row').append(influncer_list_row_new);
        
        // Update totals
        updateTotals();
        
        max_influcencer_counter++;
    })

    $(document).on("click", ".influncer-detail button.unselect-button", function() {
        var get_influncer_id = $(this).closest(".influncer-detail").attr("data-id").replace(/inflinser_id/, '');
        $('.selected-influncer-contr div[data-id="inflinser_id' + get_influncer_id + '"]').remove();

        $(".form-shadow-box.middle-box table tbody .inflinser_id" + get_influncer_id).remove();

        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"]').removeClass("selected");
        $("span.influncer-cart-count").text($(".selected-influncer-contr .influncer-detail").length);
        $("#delected-influncer").val($(".selected-influncer-contr .influncer-detail").length);

        const toggleImage = document.getElementById(`card_clickitfame_logo_inflinser_id${get_influncer_id}`);
        if (toggleImage) {
            toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}";
        }

        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"] button.unselect-button')
            .text("SELECT")
            .removeClass("unselect-button")
            .addClass("select-button");

        var get_influncer_follower = parseIntSafe($(this).closest(".influncer-detail").find(".follower-count-val").data("followers")) || 0;
        var get_influncer_price = parseFloatSafe($(this).closest(".influncer-detail").find(".user_price-val").data("price")) || 0;
        var is_small_business_owner = Boolean(parseInt($(this).closest(".influncer-detail").find("#is_small_business_owner").text() || "0"));

        // Update subtotal and follower count
        const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
        const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
        const newSubtotal = currentSubtotal - get_influncer_price;
        const newFollower = currentFollower - get_influncer_follower;

        // Update with formatted text and data attributes
        $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#follower').text(newFollower).data('followers', newFollower);
        
        // Update totals
        updateTotals();
        
        $(".inflinser_id" + get_influncer_id).remove();
    })

    $(document).on("click", ".influncer-detail button.remove-button", function() {
        var get_influncer_id = $(this).closest(".influncer-detail").attr("data-id").replace(/inflinser_id/, '');
        $(this).closest(".influncer-detail").remove();

        $(".form-shadow-box.middle-box table tbody .inflinser_id" + get_influncer_id).remove();

        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"]').removeClass("selected");
        $("span.influncer-cart-count").text($(".selected-influncer-contr .influncer-detail").length);
        $("#delected-influncer").val($(".selected-influncer-contr .influncer-detail").length);

        const toggleImage = document.getElementById(`card_clickitfame_logo_inflinser_id${get_influncer_id}`);
        if (toggleImage) {
            toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}";
        }

        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"] button.unselect-button')
            .text("SELECT")
            .removeClass("unselect-button")
            .addClass("select-button");

        var get_influncer_follower = parseIntSafe($(this).closest(".influncer-detail").find(".follower-count-val").data("followers")) || 0;
        var get_influncer_price = parseFloatSafe($(this).closest(".influncer-detail").find(".user_price-val").data("price")) || 0;
        var is_small_business_owner = Boolean(parseInt($(this).closest(".influncer-detail").find("#is_small_business_owner").text() || "0"));

        // Update subtotal and follower count
        const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
        const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
        const newSubtotal = currentSubtotal - get_influncer_price;
        const newFollower = currentFollower - get_influncer_follower;

        // Update with formatted text and data attributes
        $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#follower').text(newFollower).data('followers', newFollower);
        
        // Update totals
        updateTotals();
        
        $(".inflinser_id" + get_influncer_id).remove();
        max_influcencer_counter -= 1;
    });

    // Delete icon click handler in campaign list
    $(document).on("click", ".campaign-item .remove-icon img", function() {
        var get_influncer_id = $(this).data("delete");
        
        // Remove the influencer from various containers
        $('.selected-influncer-contr div[data-id="inflinser_id' + get_influncer_id + '"]').remove();
        $(".form-shadow-box.middle-box table tbody .inflinser_id" + get_influncer_id).remove();
        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"]').removeClass("selected");
        
        // Reset the button state
        $('.influncer-list div[data-id="inflinser_id' + get_influncer_id + '"] button.unselect-button')
            .text("SELECT")
            .removeClass("unselect-button")
            .addClass("select-button");
        
        // Update the logo image
        const toggleImage = document.getElementById(`card_clickitfame_logo_inflinser_id${get_influncer_id}`);
        if (toggleImage) {
            toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}";
        }

        // Get the follower count from the data attribute instead of text
        var get_influncer_follower = parseIntSafe($(this).closest(".campaign-item").find(".follower-count-val").data("followers")) || 0;

        // Get the price from the data attribute
        var get_influncer_price = parseFloatSafe($(this).closest(".campaign-item").find(".user_price-val").data("price")) || 0;

        // Get VAT value if available
        var vat_value = parseFloatSafe($(this).closest(".campaign-item").find("#vat_value").data("vat")) || 0;

        // Update subtotal and follower count
        const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
        const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
        const newSubtotal = currentSubtotal - get_influncer_price;
        const newFollower = currentFollower - get_influncer_follower;

        // Update all relevant elements with formatted text and data attributes
        $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#subtotal_final').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
        $('#subtotal_final_new').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);

        $('#follower').text(newFollower).data('followers', newFollower);
        $('#follower_final').text(newFollower).data('followers', newFollower);
        $('#follower_final_new').text(newFollower).data('followers', newFollower);

        // Update totals
        updateTotals();

        // Update counter and remove the element
        max_influcencer_counter -= 1;
        $(this).closest("div.campaign-list").remove();
        $(".influncer-cart span.influncer-cart-count").text(max_influcencer_counter);
    });

    // Steps navigation - next button
    $(document).on("click", ".nav-right", function() {
        var currentStep = $(this).closest(".new_steps");
        var step_number = parseInt(currentStep.attr("id").replace(/new_steps/, ""));
        var nextStep = step_number + 1;

        console.log('current step_number: ' + step_number + ', next step: ' + nextStep);

        // Validate current step inputs
        var isValid = true;
        currentStep.find(':input:not(:button)').each(function(index, value) {
            if ($(this).parsley) {
                if (!$(this).parsley().validate()) {
                    isValid = false;
                }
            }
            if ($(this).hasClass('tagify--outside')) {
                $(this).siblings('tags').find('span').attr('style', 'border:1px solid red');
            }
        });

        // Additional validation for specific steps
        if (step_number == 0) {
            // Validate social media selection
            if (!$('input[name="mp_socialmedia"]:checked').length) {
                isValid = false;
                $("#error-select-social-media").text("Please select social media.");
            } else {
                $("#error-select-social-media").text("");
            }

            // Validate post type selection
            if (!$('input[name="mp_socialmedia_type2"]:checked').length) {
                isValid = false;
                $("#error-post-type").text("Please select post type.");
            } else {
                $("#error-post-type").text("");
            }
        }

        if (step_number == 3) {
            // Validate influencer selection
            if ($(".selected-influncer-contr .influncer-detail").length == 0) {
                isValid = false;
                $("#error-select-influncer").removeClass("d-none").text("Please select at least one influencer.");
                return false;
            } else {
                $("#error-select-influncer").addClass("d-none").text("");
            }
        }

        if (isValid && nextStep < 5) {
            // Hide current step and show next step
            currentStep.hide();
            $('#new_steps' + nextStep).show();
            $('#steps-point' + nextStep).addClass("inprogress");
            $('#steps-point' + step_number).addClass("completed").removeClass("inprogress");
        


            // Load tasks for step 1 (step_number 0 -> next step 1)
            if (nextStep == 1) {
                var media = $('input[name="mp_socialmedia"]:checked').val();
                var type = $('input[name="type_post_content"]:checked').val();
                var select_type = $('input[name="mp_socialmedia_type2"]:checked').val();
                var ptc = $('input[name="ptc"]:checked').val();
                $.ajax({
                        url: "{{ url('get-tasks') }}",
                        data: {
                            media: media,
                            type: type,
                            select_type: select_type,
                            ptc: ptc
                        }
                    })
                    .done(function(data) {
                        $("#latest-task").html(data);
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Failed to load tasks:", error);
                    });
            }

            // Load task inputs for step 2 (step_number 1 -> next step 2)
            if (nextStep == 2) {
                var media = $('input[name="mp_socialmedia"]:checked').val();
                var type = $('input[name="type_post_content"]:checked').val();
                var select_type = $('input[name="mp_socialmedia_type2"]:checked').val();
                var ptc = $('input[name="ptc"]:checked').val();

                var task_additional = new Array();
                $(".task_additional:checked").each(function() {
                    task_additional.push($(this).val());
                });

                $.ajax({
                        url: "{{ url('get-tasks-input') }}",
                        data: {
                            media: media,
                            type: type,
                            select_type: select_type,
                            ptc: ptc,
                            task_additional: task_additional
                        }
                    })
                    .done(function(data) {
                        $("#latest-task-inputs").html(data);

                        // Initialize Tagify if hashtag input exists
                        setTimeout(function() {
                            var input_tag_one = document.querySelector('#hashtag1');
                            if (input_tag_one) {
                                var arrayFromPHP = $("#hashtag_arr").val();
                                if (arrayFromPHP) {
                                    new Tagify(input_tag_one, {
                                        whitelist: arrayFromPHP.split(","),
                                    });
                                }
                            }
                        }, 100);
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Failed to load task inputs:", error);
                    });
            }

            // Load influencers for step 3 (step_number 2 -> next step 3)
            if (nextStep == 3) {
                var formData = $('#form1').serialize();
                $.ajax({
                        url: "{{ url('get-influencers') }}",
                        data: formData
                    })
                    .done(function(data) {
                        $("#latest-influencer-lists").html(data);
                        $('#compaingTitle').html($('#campaign_title').val());

                        // Restore selected influencers
                        $(".selected-influncer-contr .influncer-detail").each(function(index) {
                            var selected_attribute = $(this).attr("data-id");
                            $(".influncer-list .influncer-detail[data-id=" + selected_attribute + "]").addClass("selected");

                            $(".influncer-list .influncer-detail[data-id=" + selected_attribute + "] button.select-button")
                                .text("UNSELECT")
                                .removeClass("select-button")
                                .addClass("unselect-button");

                            const toggleImage = document.getElementById(`card_clickitfame_logo_${selected_attribute}`);
                            if (toggleImage) {
                                toggleImage.src = "{{ asset('/assets/front-end/images/icons/clickitfame_white.svg') }}";
                            }
                        });

                        // Initialize load more functionality based on screen size
                        setTimeout(function() {
                            if (typeof $.fn.simpleLoadMore !== 'undefined') {
                                if ($(window).width() > 1399) {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 15,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                } else if ($(window).width() > 1024) {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 12,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                } else if ($(window).width() > 991) {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 9,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                } else if ($(window).width() > 767) {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 6,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                } else {
                                    $('.influncer-list').simpleLoadMore({
                                        item: '.influncer-detail',
                                        count: 12,
                                        counterInBtn: true,
                                        btnText: 'View More {showing}/{total}',
                                    });
                                }
                            }
                        }, 500);
                    })
                    .fail(function(xhr, status, error) {
                        console.error("Failed to load influencers:", error);
                    });
            }

        }

        if ($('#new_steps3').is(':visible')) {
            $('html, body').animate({
                scrollTop: $('html, body').offset().top,
            });
            var btn = $('body');
            btn.addClass('show');
        }
    });

    // Steps navigation - back button
    $(document).on("click", ".nav-left", function() {
        $("#ifdonthave").hide();

        var currentStep = $(this).closest(".new_steps");
        if (currentStep.attr("id")) {
            var step_number = parseInt(currentStep.attr("id").replace(/new_steps/, ""));
            var prevStep = step_number - 1;

            console.log('current step_number: ' + step_number + ', prev step: ' + prevStep);

            if (step_number == 0) {
                // If we're at the first second-level step, go back to first-level steps
                var fromStep = $(this).attr("data-from-step");
                if (fromStep == "Reaction video" || fromStep == "Survey") {
                    $(".shoutout").addClass("d-none").removeClass("d-block");
                    $(".campaign-type").addClass("d-block").removeClass("d-none");
                } else {
                    $(".shoutout").addClass("d-none").removeClass("d-block");
                    $(".boost-me").addClass("d-block").removeClass("d-none");
                }
                // Reset step indicators
                $('#steps-point0').removeClass("inprogress completed");
            } else if (prevStep >= 0) {
                // Navigate within second-level steps
                currentStep.hide();
                $('#new_steps' + prevStep).show();
                $('#steps-point' + step_number).removeClass("inprogress");
                $('#steps-point' + prevStep).addClass("inprogress").removeClass("completed");
            }

            $(".alert-select-option").addClass("d-none");
        }
    })

    $(document).ready(function() {
        $(document).on('change', '.checkFilter', function() {
            getVenueAjax();
        });
    });

    function getVenueAjax() {
        const dataString = $("#form1").serialize();
        $.ajax({
            url: "{{ url('/market-step-filter') }}",
            data: dataString
        }).done(function(data) {
            $("#latest-influencer-lists").html(data);
            
            $(".selected-influncer-contr .influncer-detail").each(function() {
                const selected_attribute = $(this).attr("data-id");
                $(".influncer-list .influncer-detail[data-id=" + selected_attribute + "]").addClass("selected");
            });
            
            $(".select").select2();
            $("#hashtags").select2({
                placeholder: "Select",
                multiple: true
            });
        }).fail(function(xhr, status, error) {
            console.error("AJAX request failed:", error);
            toastr.error("Failed to load influencers. Please try again.");
        });
    }

    $(document).on('click', '.resetFilter', function(e) {
        $('#sort_by option[value=""]').attr('selected', 'selected');
        $('#target_age option[value=""]').attr('selected', 'selected');
        $('#language option[value=""]').attr('selected', 'selected');
        $('#content_attracts option[value=""]').attr('selected', 'selected');
        $('#hashtags').val(null).trigger('change');
        $('#influencer_type option[value=""]').attr('selected', 'selected');
        $('#gender option[value=""]').attr('selected', 'selected');
        $('#target_gender option[value=""]').attr('selected', 'selected');
        $('#rank option[value=""]').attr('selected', 'selected');

        $('#followers option[value=""]').attr('selected', 'selected');
        $('#amount-followers').val('0');
        $('#amount-price').val('0');
        getVenueAjax();
    });

    $('.nuwidth-dynamic.proba.dvambers').keyup(function() {
        this.value = this.value.replace(/[^0-9\.]/g, '');
    });

    $(document).on("change", "#selected-files", function() {
        var numFiles = $("input", this)[0].files.length;
    });

    $(document).on("click", ".button-Request", function() {
        if ($(".marketplace-finish .form-shadow-box table tbody").children().length === 0) {
            $("#ifdonthave").show();
            toastr.error("Please select at least one influencer before proceeding.");
            return false;
        } else {
            $("#ifdonthave").hide();
            $("#pageLoader").show();
            // Ensure form is valid before submission
            const $form = $(this).closest('form');
            if ($form.length && typeof $form.parsley === 'function') {
                return $form.parsley().validate();
            }
        }
    })
</script>
