<div class="steps-section">
    <h2>What should the influencer boost?</h2>
</div>
{{-- MP-Steps --}}
<div class="page_tab new_steps">
    <div class="alert-select-option d-none mb-3">
        Please seclect an option.
    </div>
    <div class="social_media boost d-flex flex-column align-items-center justify-content-between flex-wrap">
        <div class="social_media_radio campaign-type select-type-post">
            <div class="campaign-type-content">
                <input type="radio" value="content" name="ptc" id="type-post-content">
                <label for="type-post-content">
                    <img src="{{ asset('/assets/front-end/images/icons/icon-bost-share.svg') }}" class="" alt="">
                    <div class="campaign-type-text">Share Content</div>
                    <div class="campaign-type-share-information">The influencer will share your instagram content</div>
                </label>
            </div>
        </div>
        <div class="social_media_radio campaign-type select-type-post">
            <div class="campaign-type-content">
                <input type="radio" value="photo" name="ptc" id="type-post-photo">
                <label for="type-post-photo">
                    <img src="{{ asset('/assets/front-end/images/icons/icon-bost-photo.svg') }}" class="" alt="">
                    <div class="campaign-type-text">Photo</div>
                    <div class="campaign-type-share-information">The influencer will post your photo </div>
                </label>
            </div>
        </div>
        <div class="social_media_radio campaign-type select-type-post">
            <div class="campaign-type-content">
                <input type="radio" value="video" name="ptc" id="type-post-video">
                <label for="type-post-video">
                    <img src="{{ asset('/assets/front-end/images/icons/icon-bost-video.svg') }}" class="" alt="">
                    <div class="campaign-type-text">Video</div>
                    <div class="campaign-type-share-information">The influencer will post your video </div>
                </label>
            </div>
        </div>
    </div>
    <div class="step-nevigationbutton">
        <div class="nav-left start-prev-step">
            <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" class="" alt="">
        </div>
        <div class="nav-right start-next-step">
            <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" class="" alt="">
        </div>
    </div>
</div>
<script>
    $(document).on("click", ".campaign-type a", function(){
        var typeOn = $(this).attr("data-boost-value");
        $("#soc-type").val(typeOn);
        $(".media_platform_post_type").find("."+typeOn).show();
    });

    // Handle selection of boost type and enable next button
    $(document).on("click", ".select-type-post", function(){
        console.log('Boost type selected:', $(this).find('input[name="ptc"]').val());

        // Mark the option as selected
        $(".select-type-post").removeClass("selected");
        $(this).addClass("selected");

        // Store the selected value
        var selectedValue = $(this).find('input[name="ptc"]').val();
        $("#soc-type").val(selectedValue);

        // Enable the next button
        $(".start-next-step").removeClass("disabled").css("opacity", "1").css("pointer-events", "auto");

        // Hide any error messages
        $(".alert-select-option").addClass("d-none");
    });

    // Handle next button click
    $(document).on("click", ".boost-me .start-next-step", function(){
        // Check if a boost type is selected
        if (!$('input[name="ptc"]:checked').length) {
            $(".alert-select-option").removeClass("d-none");
            window.scrollTo(0, 0);
            return false;
        }

        // Hide alert message
        $(".alert-select-option").addClass("d-none");

        // Hide current step and show next step
        $(this).closest(".marketplace-imports").addClass("d-none");
        $(this).closest(".marketplace-imports").next(".shoutout").removeClass("d-none");

        // Initialize the second-level steps
        var selectedValue = $('input[name="ptc"]:checked').val();
        $(".media_platform_post_type").find(".get_type").hide();
        $(".media_platform_post_type").find(".get_type." + selectedValue).show();
        $("#steps-point0").addClass("inprogress");
        $("#new_steps0").show();
    });

    // Handle back navigation from step-two
    $(document).on("click", ".boost-me .start-prev-step", function() {
        console.log('Navigating back from step-two to step-one');

        $(".boost-me").addClass("d-none").removeClass("d-block");
        $(".campaign-type").addClass("d-block").removeClass("d-none");

        // Clear any previous selections to allow re-selection
        $('input[name="ptc"]').prop('checked', false);
        $(".select-type-post").removeClass("selected");
        $(".start-next-step").addClass("disabled").css("opacity", "0.5").css("pointer-events", "none");
        $(".alert-select-option").addClass("d-none");
    });

    // Initially disable the next button
    $(document).ready(function(){
        $(".start-next-step").addClass("disabled").css("opacity", "0.5").css("pointer-events", "none");
    });
</script>